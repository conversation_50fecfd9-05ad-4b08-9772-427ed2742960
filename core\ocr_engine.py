"""
OCR核心引擎
"""

import os
import logging
import numpy as np
import cv2
from typing import List, Dict, Any, Optional, Callable, Union
from pathlib import Path
from PIL import Image
# 移除PyQt6依赖，使用回调函数机制, QThread

try:
    import paddleocr
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False

from config.settings import OCRSettings
from config.models_config import ModelsConfig
from utils.image_utils import ImageUtils


class OCRResult:
    """OCR识别结果"""
    
    def __init__(self):
        self.text_blocks: List[Dict[str, Any]] = []
        self.tables: List[Dict[str, Any]] = []
        self.layout_info: Dict[str, Any] = {}
        self.confidence_scores: List[float] = []
        self.processing_time: float = 0.0
        self.page_number: int = 0
    
    def add_text_block(self, text: str, bbox: List[float], confidence: float):
        """添加文本块"""
        self.text_blocks.append({
            'text': text,
            'bbox': bbox,
            'confidence': confidence
        })
        self.confidence_scores.append(confidence)
    
    def add_table(self, table_data: Dict[str, Any]):
        """添加表格数据"""
        self.tables.append(table_data)
    
    def get_all_text(self) -> str:
        """获取所有识别的文本"""
        return '\n'.join([block['text'] for block in self.text_blocks])
    
    def get_average_confidence(self) -> float:
        """获取平均置信度"""
        if not self.confidence_scores:
            return 0.0
        return sum(self.confidence_scores) / len(self.confidence_scores)


class OCREngine:
    """OCR引擎"""
    
    def __init__(self, settings: OCRSettings, models_config: ModelsConfig):
        self.settings = settings
        self.models_config = models_config
        self.logger = logging.getLogger(__name__)

        # OCR实例
        self.ocr_general = None
        self.ocr_table = None
        self.ocr_layout = None

        # 初始化状态
        self.is_initialized = False
        self.current_mode = 'general'  # 'general' 或 'table'

        # 回调函数
        self.progress_callback = None
        self.page_processed_callback = None
        self.error_callback = None
    
    def initialize(self) -> bool:
        """初始化OCR引擎"""
        if not PADDLEOCR_AVAILABLE:
            if self.error_callback:
                self.error_callback("PaddleOCR未安装，请安装相关依赖")
            return False
        
        try:
            if self.progress_callback:
                self.progress_callback(10, "正在初始化OCR引擎...")

            # 获取模型路径
            ocr_model_paths = self.models_config.get_ocr_model_paths()
            table_model_paths = self.models_config.get_table_model_paths()

            # 初始化通用OCR - 使用兼容的参数
            ocr_kwargs = {
                'use_angle_cls': self.settings.use_angle_cls,
                'lang': self.settings.lang,
                'det_db_thresh': 0.3,  # 检测阈值，降低以提高检测敏感度
                'det_db_box_thresh': 0.6,  # 边界框阈值
                'rec_batch_num': 6  # 识别批次大小
            }

            # 注意：use_space_char在新版本PaddleOCR中已被移除，不再添加此参数

            # 添加自定义模型路径
            if ocr_model_paths:
                ocr_kwargs.update(ocr_model_paths)

            if self.progress_callback:
                self.progress_callback(30, "正在加载通用OCR模型...")
            self.ocr_general = PaddleOCR(**ocr_kwargs)

            # 初始化表格识别（如果需要）
            if table_model_paths:
                if self.progress_callback:
                    self.progress_callback(60, "正在加载表格识别模型...")
                try:
                    from paddleocr import PPStructure
                    structure_kwargs = {}
                    structure_kwargs.update(table_model_paths)
                    self.ocr_table = PPStructure(**structure_kwargs)
                except ImportError:
                    self.logger.warning("PPStructure不可用，表格模式将使用通用OCR")

            if self.progress_callback:
                self.progress_callback(100, "OCR引擎初始化完成")
            self.is_initialized = True
            return True
            
        except Exception as e:
            error_msg = f"OCR引擎初始化失败: {e}"
            self.logger.error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
            return False
    
    def set_mode(self, mode: str):
        """设置处理模式"""
        if mode in ['general', 'table']:
            self.current_mode = mode
        else:
            raise ValueError(f"不支持的模式: {mode}")
    
    def process_image(self, image: Union[np.ndarray, Image.Image, str, Path], 
                     page_number: int = 0) -> OCRResult:
        """
        处理单张图像
        
        Args:
            image: 图像数据（numpy数组、PIL图像或文件路径）
            page_number: 页码
            
        Returns:
            OCR识别结果
        """
        if not self.is_initialized:
            raise RuntimeError("OCR引擎未初始化")
        
        result = OCRResult()
        result.page_number = page_number
        
        try:
            import time
            start_time = time.time()
            
            # 预处理图像
            processed_image = self._preprocess_image(image)
            
            if self.current_mode == 'general':
                result = self._process_general_ocr(processed_image, result)
            elif self.current_mode == 'table':
                result = self._process_table_ocr(processed_image, result)
            
            result.processing_time = time.time() - start_time
            
            # 调用页面处理完成回调
            if self.page_processed_callback:
                self.page_processed_callback(page_number, result)
            
            return result
            
        except Exception as e:
            error_msg = f"图像处理失败 (页面 {page_number}): {e}"
            self.logger.error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
            raise
    
    def _preprocess_image(self, image: Union[np.ndarray, Image.Image, str, Path]) -> np.ndarray:
        """预处理图像"""
        if isinstance(image, (str, Path)):
            # 从文件路径加载
            image = ImageUtils.load_image(image)
        elif isinstance(image, Image.Image):
            # PIL图像转OpenCV
            image = ImageUtils.pil_to_cv2(image)
        elif isinstance(image, np.ndarray):
            # 已经是numpy数组
            pass
        else:
            raise ValueError(f"不支持的图像类型: {type(image)}")

        # 记录原始图像信息
        self.logger.debug(f"预处理图像尺寸: {image.shape}")
        self.logger.debug(f"图像数据类型: {image.dtype}")

        # 图像质量检查
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        mean_brightness = np.mean(gray)
        self.logger.debug(f"图像平均亮度: {mean_brightness}")

        # 如果图像过暗或过亮，进行预处理
        if mean_brightness < 50 or mean_brightness > 200:
            self.logger.info(f"图像亮度异常({mean_brightness})，应用增强处理")
            image = self._enhance_image_quality(image)

        return image

    def _enhance_image_quality(self, image: np.ndarray) -> np.ndarray:
        """增强图像质量以提高OCR识别率"""
        try:
            # 转换为灰度图进行分析
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # 自适应直方图均衡化
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced_gray = clahe.apply(gray)

            # 如果原图是彩色的，保持彩色格式
            if len(image.shape) == 3:
                # 转换到LAB色彩空间进行亮度增强
                lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
                lab[:,:,0] = enhanced_gray
                enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
            else:
                enhanced = enhanced_gray

            self.logger.debug("应用了图像质量增强")
            return enhanced

        except Exception as e:
            self.logger.warning(f"图像增强失败，使用原图: {e}")
            return image

    def _process_general_ocr(self, image: np.ndarray, result: OCRResult) -> OCRResult:
        """处理通用OCR"""
        try:
            self.logger.debug("开始执行OCR识别...")

            # 执行OCR识别
            ocr_results = self.ocr_general.predict(image)

            # 详细记录OCR原始结果
            self.logger.debug(f"OCR原始结果类型: {type(ocr_results)}")
            self.logger.debug(f"OCR结果长度: {len(ocr_results) if ocr_results else 0}")

            if not ocr_results:
                self.logger.warning("OCR返回None结果")
                return result

            if not ocr_results[0]:
                self.logger.warning("OCR返回空列表结果")
                return result

            detected_lines = len(ocr_results[0])
            self.logger.info(f"检测到 {detected_lines} 个文本行")

            # 解析结果
            valid_blocks = 0
            for i, line in enumerate(ocr_results[0]):
                try:
                    if len(line) >= 2:
                        bbox = line[0]  # 边界框坐标
                        text_info = line[1]  # (文本, 置信度)

                        if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                            text = text_info[0]
                            confidence = text_info[1]

                            # 过滤低置信度和空文本
                            if confidence > 0.1 and text.strip():
                                # 转换边界框格式
                                bbox_flat = [coord for point in bbox for coord in point]
                                result.add_text_block(text, bbox_flat, confidence)
                                valid_blocks += 1

                                # 记录前几个识别结果用于调试
                                if i < 3:
                                    self.logger.debug(f"文本块{i}: '{text}' (置信度: {confidence:.3f})")
                        else:
                            self.logger.debug(f"文本行{i}格式异常: {text_info}")
                    else:
                        self.logger.debug(f"文本行{i}长度不足: {len(line)}")

                except Exception as e:
                    self.logger.warning(f"解析文本行{i}失败: {e}")
                    continue

            self.logger.info(f"成功解析 {valid_blocks} 个有效文本块")

            # 如果没有识别到有效文本，尝试降低阈值重试
            if valid_blocks == 0 and detected_lines > 0:
                self.logger.info("未识别到有效文本，尝试降低置信度阈值重试...")
                return self._retry_with_lower_threshold(image, result)

            return result

        except Exception as e:
            self.logger.error(f"通用OCR处理失败: {e}")
            raise

    def _retry_with_lower_threshold(self, image: np.ndarray, result: OCRResult) -> OCRResult:
        """使用更低的置信度阈值重试OCR识别"""
        try:
            self.logger.info("使用降低的置信度阈值重试OCR识别...")

            # 重新创建OCR实例，使用更宽松的参数
            retry_kwargs = {
                'use_angle_cls': self.settings.use_angle_cls,
                'lang': self.settings.lang,
                'det_db_thresh': 0.1,  # 大幅降低检测阈值
                'det_db_box_thresh': 0.3,  # 降低边界框阈值
                'rec_batch_num': 6
            }

            # 临时创建新的OCR实例
            temp_ocr = PaddleOCR(**retry_kwargs)
            ocr_results = temp_ocr.predict(image)

            if ocr_results and ocr_results[0]:
                self.logger.info(f"重试检测到 {len(ocr_results[0])} 个文本行")

                valid_blocks = 0
                for i, line in enumerate(ocr_results[0]):
                    try:
                        if len(line) >= 2:
                            bbox = line[0]
                            text_info = line[1]

                            if isinstance(text_info, (list, tuple)) and len(text_info) >= 2:
                                text = text_info[0]
                                confidence = text_info[1]

                                # 使用更低的置信度阈值
                                if confidence > 0.05 and text.strip():
                                    bbox_flat = [coord for point in bbox for coord in point]
                                    result.add_text_block(text, bbox_flat, confidence)
                                    valid_blocks += 1

                                    if i < 3:
                                        self.logger.debug(f"重试文本块{i}: '{text}' (置信度: {confidence:.3f})")
                    except Exception as e:
                        self.logger.warning(f"重试解析文本行{i}失败: {e}")
                        continue

                self.logger.info(f"重试成功解析 {valid_blocks} 个有效文本块")

            return result

        except Exception as e:
            self.logger.error(f"重试OCR识别失败: {e}")
            return result
    
    def _process_table_ocr(self, image: np.ndarray, result: OCRResult) -> OCRResult:
        """处理表格OCR"""
        try:
            if self.ocr_table:
                # 使用PPStructure进行表格识别
                structure_results = self.ocr_table(image)
                
                for item in structure_results:
                    if item['type'] == 'table':
                        # 表格数据
                        table_data = {
                            'type': 'table',
                            'bbox': item['bbox'],
                            'html': item.get('res', {}).get('html', ''),
                            'confidence': item.get('confidence', 0.0)
                        }
                        result.add_table(table_data)
                    elif item['type'] == 'text':
                        # 文本数据
                        text = item.get('res', '')
                        bbox = item.get('bbox', [])
                        confidence = item.get('confidence', 0.0)
                        
                        result.add_text_block(text, bbox, confidence)
            else:
                # 回退到通用OCR
                result = self._process_general_ocr(image, result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"表格OCR处理失败: {e}")
            # 回退到通用OCR
            return self._process_general_ocr(image, result)
    
    def process_multiple_images(self, images: List[Union[np.ndarray, Image.Image, str, Path]], 
                              progress_callback: Optional[Callable] = None) -> List[OCRResult]:
        """
        处理多张图像
        
        Args:
            images: 图像列表
            progress_callback: 进度回调函数
            
        Returns:
            OCR结果列表
        """
        results = []
        total_images = len(images)
        
        for i, image in enumerate(images):
            try:
                # 更新进度
                progress = int((i / total_images) * 100)
                status = f"正在处理第 {i+1}/{total_images} 页"
                if self.progress_callback:
                    self.progress_callback(progress, status)
                
                if progress_callback:
                    progress_callback(progress, status)
                
                # 处理图像
                result = self.process_image(image, page_number=i+1)
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"处理第 {i+1} 页失败: {e}")
                # 创建空结果
                empty_result = OCRResult()
                empty_result.page_number = i+1
                results.append(empty_result)
        
        # 完成进度
        self.progress_updated.emit(100, f"处理完成，共 {total_images} 页")
        
        return results
    
    def cleanup(self):
        """清理资源"""
        self.ocr_general = None
        self.ocr_table = None
        self.ocr_layout = None
        self.is_initialized = False
